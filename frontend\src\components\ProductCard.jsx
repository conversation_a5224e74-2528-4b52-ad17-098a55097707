import { Box, Button, Heading, IconButton, Image, useColorModeValue } from '@chakra-ui/react'
import { Text } from '@chakra-ui/react'
import { HStack } from '@chakra-ui/react'
import { EditIcon, DeleteIcon } from '@chakra-ui/icons'


const ProductCard = (product) => {
    const textColor = useColorModeValue("gray.600", "gray.200");
    const bg = useColorModeValue("white", "gray.800")
  return <Box 
    w={"full"}
    bg={bg}
    p={6}
    rounded={"lg"}
    shadow={"md"}
    _hover ={{
      transform: "scale(1.05)",
      transition: "transform 0.2s ease-in-out",
    }}
  >
    <Image src={product.image} alt={product.name} h={48} objectFit={"cover"} w={"full"}/>
    <Box p={4}>
        <Heading as={'h3'} size={"lg"} mb={2}>
            {product.name}
        </Heading>
        <Text fontWeight={"bold"} fontSize={"xl"} color={textColor} mb={2}>
            ${product.price}
        </Text>
        <HStack spacing={4}>
            <IconButton icon={<EditIcon/>} colorScheme='blue'/>
            <IconButton icon={<DeleteIcon/>} colorScheme='red'/>
        </HStack>

    </Box>

  </Box>
}

export default ProductCard