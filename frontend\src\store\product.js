import {create} from "zustand";

export const useProductStore = create((set)=>({
    product:[],
    setProduct:(product) =>set({product}),
    createProduct: async(newProduct)=>{
        if(!newProduct.name||!newProduct.price||!newProduct.image){
            return{success: false, message: "Please fill all the fields"};
        }
        const res =await fetch("/api/products",{
            method:"POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(newProduct), 
        })
        const data = await res.json();
        set((state)=>({product: [...state.product, data.newProduct]}));
        return{success: true, message: "Product created"};

        
    },
    fetchProducts: async()=>{
        const res = await fetch("/api/products");
        const data = await res.json();
        set({product: data});
    },
}));

